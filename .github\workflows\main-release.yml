# Main分支自动打包发布流程
# 当代码合并到main分支时触发多平台多架构的自动打包和发布
name: 自动打包发布

on:
  push:
    branches: [ main ]
  workflow_dispatch:
    inputs:
      version:
        description: '发布版本号 (例如: v1.0.0)'
        required: true
        default: 'v1.0.0'
      include_arm64:
        description: '是否包含ARM64架构 (实验性功能)'
        required: false
        default: false
        type: boolean

jobs:
  # 发布前检查
  pre-release-check:
    name: 发布前检查
    runs-on: ubuntu-latest

    steps:
    - name: 检出代码
      uses: actions/checkout@v4

    - name: 设置Python环境
      uses: actions/setup-python@v5
      with:
        python-version: '3.11'

    - name: 安装依赖
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt

    - name: 运行完整测试
      run: |
        echo "🧪 运行发布前测试..."
        python scripts/run_tests.py
        pytest tests/ -v --cov=src

    - name: 检查版本信息和构建配置
      run: |
        echo "📋 检查版本信息..."
        echo "当前提交: ${{ github.sha }}"
        echo "分支: ${{ github.ref }}"
        echo "包含ARM64: ${{ github.event.inputs.include_arm64 || 'false' }}"

        # 检查图标文件
        echo "🎨 检查应用图标文件..."
        ls -la src/resources/icons/

        # 验证构建脚本
        echo "🔧 验证构建脚本..."
        python scripts/build.py --test-only

  # 构建发布版本 - 支持多平台多架构
  build-release:
    name: 构建发布版本
    needs: pre-release-check
    strategy:
      fail-fast: false
      matrix:
        include:
          # Windows x86_64 (主要支持)
          - os: windows-latest
            platform: windows
            arch: x86_64
            runner_arch: X64

          # Linux x86_64 (主要支持)
          - os: ubuntu-latest
            platform: linux
            arch: x86_64
            runner_arch: X64

          # 未来ARM64支持预留 (当前为实验性)
          # - os: windows-latest
          #   platform: windows
          #   arch: arm64
          #   runner_arch: ARM64
          #   experimental: true
          #
          # - os: ubuntu-latest
          #   platform: linux
          #   arch: arm64
          #   runner_arch: ARM64
          #   experimental: true

    runs-on: ${{ matrix.os }}

    steps:
    - name: 检出代码
      uses: actions/checkout@v4

    - name: 设置Python环境
      uses: actions/setup-python@v5
      with:
        python-version: '3.11'
        architecture: ${{ matrix.runner_arch }}

    - name: 安装系统依赖 (Linux)
      if: matrix.platform == 'linux'
      run: |
        sudo apt-get update
        sudo apt-get install -y \
          libgl1-mesa-glx \
          libglib2.0-0 \
          libxkbcommon-x11-0 \
          libxcb-icccm4 \
          libxcb-image0 \
          libxcb-keysyms1 \
          libxcb-randr0 \
          libxcb-render-util0 \
          libxcb-xinerama0 \
          libxcb-xfixes0

    - name: 安装Python依赖
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install pyinstaller

    - name: 配置构建环境
      run: |
        echo "🔧 配置 ${{ matrix.platform }}-${{ matrix.arch }} 构建环境..."
        echo "平台: ${{ matrix.platform }}"
        echo "架构: ${{ matrix.arch }}"
        echo "运行器架构: ${{ matrix.runner_arch }}"

        # 设置环境变量
        echo "BUILD_PLATFORM=${{ matrix.platform }}" >> $GITHUB_ENV
        echo "BUILD_ARCH=${{ matrix.arch }}" >> $GITHUB_ENV

    - name: 构建可执行文件
      run: |
        echo "🔨 构建 ${{ matrix.platform }}-${{ matrix.arch }} 版本..."

        # 使用平台特定的构建参数
        if [[ "${{ matrix.platform }}" == "windows" ]]; then
          python scripts/build.py --windows
        else
          python scripts/build.py --linux
        fi
      shell: bash

    - name: 验证构建结果
      run: |
        echo "✅ 验证 ${{ matrix.platform }}-${{ matrix.arch }} 构建结果..."

        # 检查构建产物
        if [[ "${{ matrix.platform }}" == "windows" ]]; then
          EXECUTABLE="dist/windows/EmailDomainManager.exe"
        else
          EXECUTABLE="dist/linux/EmailDomainManager"
        fi

        if [[ -f "$EXECUTABLE" ]]; then
          echo "✅ 可执行文件构建成功: $EXECUTABLE"
          ls -la "$EXECUTABLE"

          # 检查文件大小
          SIZE=$(stat -c%s "$EXECUTABLE" 2>/dev/null || stat -f%z "$EXECUTABLE" 2>/dev/null || echo "0")
          SIZE_MB=$((SIZE / 1024 / 1024))
          echo "📦 文件大小: ${SIZE_MB}MB"

          if [[ $SIZE_MB -lt 10 ]]; then
            echo "⚠️  警告: 文件大小异常小，可能构建不完整"
          fi
        else
          echo "❌ 可执行文件未找到: $EXECUTABLE"
          echo "📁 dist目录内容:"
          find dist/ -type f -ls 2>/dev/null || ls -la dist/ 2>/dev/null || echo "dist目录不存在"
          exit 1
        fi
      shell: bash

    - name: 创建发布包
      run: |
        echo "📦 创建 ${{ matrix.platform }}-${{ matrix.arch }} 发布包..."

        # 创建发布目录
        RELEASE_DIR="release-${{ matrix.platform }}-${{ matrix.arch }}"
        mkdir -p "$RELEASE_DIR"

        # 复制可执行文件和相关文件
        if [[ "${{ matrix.platform }}" == "windows" ]]; then
          cp -r dist/windows/* "$RELEASE_DIR/"
          ARCHIVE_NAME="EmailDomainManager-${{ matrix.platform }}-${{ matrix.arch }}.zip"

          # Windows平台使用zip打包
          if command -v 7z >/dev/null 2>&1; then
            7z a "$ARCHIVE_NAME" "$RELEASE_DIR"/*
          else
            # 使用PowerShell压缩 (Windows runner)
            powershell -Command "Compress-Archive -Path '$RELEASE_DIR\*' -DestinationPath '$ARCHIVE_NAME'"
          fi
        else
          cp -r dist/linux/* "$RELEASE_DIR/"
          ARCHIVE_NAME="EmailDomainManager-${{ matrix.platform }}-${{ matrix.arch }}.tar.gz"

          # Linux平台使用tar.gz打包
          tar -czf "$ARCHIVE_NAME" -C "$RELEASE_DIR" .
        fi

        echo "✅ 发布包创建完成: $ARCHIVE_NAME"
        ls -la "$ARCHIVE_NAME"

        # 设置输出变量
        echo "ARCHIVE_NAME=$ARCHIVE_NAME" >> $GITHUB_ENV
        echo "RELEASE_DIR=$RELEASE_DIR" >> $GITHUB_ENV
      shell: bash

    - name: 上传构建产物
      uses: actions/upload-artifact@v4
      with:
        name: build-${{ matrix.platform }}-${{ matrix.arch }}
        path: |
          ${{ env.ARCHIVE_NAME }}
          ${{ env.RELEASE_DIR }}/
        retention-days: 30

  # 创建GitHub Release
  create-release:
    name: 创建GitHub Release
    needs: build-release
    runs-on: ubuntu-latest

    steps:
    - name: 检出代码
      uses: actions/checkout@v4

    - name: 下载所有构建产物
      uses: actions/download-artifact@v4
      with:
        path: artifacts/

    - name: 整理构建产物
      run: |
        echo "📁 整理构建产物..."
        find artifacts/ -name "*.zip" -o -name "*.tar.gz" | while read file; do
          echo "发现构建产物: $file"
          cp "$file" ./
        done

        echo "📦 最终发布文件:"
        ls -la *.zip *.tar.gz 2>/dev/null || echo "没有找到发布文件"

    - name: 生成发布说明
      run: |
        echo "📝 生成发布说明..."

        # 获取最新的提交信息作为发布说明
        RELEASE_NOTES=$(git log --pretty=format:"- %s" -10)

        cat > release-notes.md << EOF
        # 域名邮箱管理器 多平台发布版本

        ## 🎉 新功能和改进

        $RELEASE_NOTES

        ## 📦 下载说明

        ### 支持的平台和架构

        - **Windows x86_64**: 下载 \`EmailDomainManager-windows-x86_64.zip\`
        - **Linux x86_64**: 下载 \`EmailDomainManager-linux-x86_64.tar.gz\`

        ### 🔮 未来支持计划

        - **Windows ARM64**: 计划支持 (适用于ARM处理器的Windows设备)
        - **Linux ARM64**: 计划支持 (适用于ARM服务器和设备)

        ## 🚀 安装说明

        ### Windows用户
        1. 下载 \`EmailDomainManager-windows-x86_64.zip\`
        2. 解压到任意目录
        3. 双击 \`EmailDomainManager.exe\` 运行

        ### Linux用户
        1. 下载 \`EmailDomainManager-linux-x86_64.tar.gz\`
        2. 解压: \`tar -xzf EmailDomainManager-linux-x86_64.tar.gz\`
        3. 添加执行权限: \`chmod +x EmailDomainManager\`
        4. 运行: \`./EmailDomainManager\`

        ## 📋 系统要求

        - **Windows**: Windows 10+ (x86_64)
        - **Linux**: 现代Linux发行版 (x86_64)，支持X11或Wayland
        - **内存**: 4GB+ RAM (推荐)
        - **存储**: 100MB+ 磁盘空间

        ## 🎨 应用特性

        - 基于PyQt6的现代化界面
        - Material Design设计风格
        - 支持Windows (.ico) 和 Linux (.png) 平台图标
        - 完整的邮箱管理功能

        ## 🐛 问题反馈

        如有问题请在 [Issues](https://github.com/your-username/email-domain-manager/issues) 中反馈。
        EOF

    - name: 确定版本号
      id: version
      run: |
        if [[ "${{ github.event_name }}" == "workflow_dispatch" ]]; then
          VERSION="${{ github.event.inputs.version }}"
        else
          # 自动生成版本号：v年.月.日-构建号
          VERSION="v$(date +'%Y.%m.%d')-${{ github.run_number }}"
        fi
        echo "version=$VERSION" >> $GITHUB_OUTPUT
        echo "发布版本: $VERSION"

    - name: 创建Release
      uses: softprops/action-gh-release@v1
      with:
        tag_name: ${{ steps.version.outputs.version }}
        name: 域名邮箱管理器 ${{ steps.version.outputs.version }}
        body_path: release-notes.md
        draft: false
        prerelease: false
        files: |
          EmailDomainManager-windows-x86_64.zip
          EmailDomainManager-linux-x86_64.tar.gz
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

  # 部署文档
  deploy-docs:
    name: 部署文档
    needs: create-release
    runs-on: ubuntu-latest

    steps:
    - name: 检出代码
      uses: actions/checkout@v4

    - name: 设置Python环境
      uses: actions/setup-python@v5
      with:
        python-version: '3.11'

    - name: 安装文档工具
      run: |
        python -m pip install --upgrade pip
        pip install mkdocs mkdocs-material

    - name: 构建文档
      run: |
        echo "📚 构建项目文档..."

        # 创建mkdocs配置
        cat > mkdocs.yml << EOF
        site_name: 域名邮箱管理器
        site_description: 基于PyQt6的域名邮箱管理工具 - 多平台自动打包发布

        nav:
          - 首页: README.md
          - 下载安装: docs/installation.md
          - 使用指南: docs/user-guide.md
          - 开发文档: docs/development.md
          - API规范: docs/api-specification.md
          - 更新日志: docs/changelog.md

        theme:
          name: material
          language: zh
          palette:
            - scheme: default
              primary: blue
              accent: blue
              toggle:
                icon: material/brightness-7
                name: 切换到深色模式
            - scheme: slate
              primary: blue
              accent: blue
              toggle:
                icon: material/brightness-4
                name: 切换到浅色模式
          features:
            - navigation.tabs
            - navigation.sections
            - navigation.expand
            - navigation.top
            - search.highlight
            - search.share
            - content.code.copy

        markdown_extensions:
          - pymdownx.highlight
          - pymdownx.superfences
          - pymdownx.tabbed
          - admonition
          - pymdownx.details
        EOF

        # 创建基础文档结构
        mkdir -p docs

        # 创建安装文档
        cat > docs/installation.md << EOF
        # 下载与安装

        ## 支持的平台

        - Windows x86_64
        - Linux x86_64

        ## 下载地址

        请访问 [GitHub Releases](https://github.com/your-username/email-domain-manager/releases) 下载最新版本。

        ## 安装说明

        ### Windows
        1. 下载 \`EmailDomainManager-windows-x86_64.zip\`
        2. 解压到任意目录
        3. 双击 \`EmailDomainManager.exe\` 运行

        ### Linux
        1. 下载 \`EmailDomainManager-linux-x86_64.tar.gz\`
        2. 解压并运行
        EOF

        mkdocs build

    - name: 部署到GitHub Pages
      uses: peaceiris/actions-gh-pages@v3
      with:
        github_token: ${{ secrets.GITHUB_TOKEN }}
        publish_dir: ./site

  # 发布通知和总结
  release-notification:
    name: 发布通知
    needs: [create-release, deploy-docs]
    runs-on: ubuntu-latest
    if: always()

    steps:
    - name: 发布结果通知
      run: |
        echo "🎉 多平台自动打包发布流程完成"
        echo "================================"

        # 检查各个任务的执行结果
        echo "📋 任务执行结果:"
        echo "  - 创建Release: ${{ needs.create-release.result }}"
        echo "  - 部署文档: ${{ needs.deploy-docs.result }}"

        if [[ "${{ contains(needs.*.result, 'failure') }}" == "true" ]]; then
          echo ""
          echo "❌ 发布过程中出现问题"
          echo "失败的任务:"
          [[ "${{ needs.create-release.result }}" == "failure" ]] && echo "  - 创建Release失败"
          [[ "${{ needs.deploy-docs.result }}" == "failure" ]] && echo "  - 部署文档失败"
          echo ""
          echo "请检查上述失败任务的日志以获取详细错误信息。"
        else
          echo ""
          echo "🎉 发布成功完成！"
          echo "📦 新版本已发布到GitHub Releases"
          echo "📚 文档已更新到GitHub Pages"
          echo ""
          echo "🔗 相关链接:"
          echo "  - Releases: https://github.com/${{ github.repository }}/releases"
          echo "  - 文档: https://${{ github.repository_owner }}.github.io/${{ github.event.repository.name }}"
        fi

        echo ""
        echo "📊 构建统计:"
        echo "  - 支持平台: Windows x86_64, Linux x86_64"
        echo "  - 构建时间: $(date)"
        echo "  - 提交SHA: ${{ github.sha }}"
